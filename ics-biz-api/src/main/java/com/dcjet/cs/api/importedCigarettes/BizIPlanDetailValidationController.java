package com.dcjet.cs.api.importedCigarettes;

import com.dcjet.cs.dto.importedCigarettes.BizIPlanDetailValidationResult;
import com.dcjet.cs.dto.importedCigarettes.BizIPlanListParam;
import com.dcjet.cs.importedCigarettes.service.BizIPlanDetailValidationService;
import com.xdo.common.token.UserInfoToken;
import com.xdo.domain.ResultObject;
import com.xdo.springboot.BaseController;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;

/**
 * 进口计划表体数据校验控制器
 *
 * <AUTHOR>
 * @date: 2025-3-13
 */
@Slf4j
@RestController
@RequestMapping("v1/bizIPlanDetailValidation")
@Api(tags = "进口计划表体数据校验接口")
public class BizIPlanDetailValidationController extends BaseController {

    @Resource
    private BizIPlanDetailValidationService bizIPlanDetailValidationService;

    /**
     * 校验表体数据列表
     *
     * @param details 表体数据列表
     * @param userInfo 用户信息
     * @return 校验结果
     */
    @PostMapping("/validateDetails")
    @ApiOperation(value = "校验表体数据列表", notes = "对进口计划表体数据进行全面校验，包括必填字段、格式、重复性等")
    public ResponseEntity<ResultObject<BizIPlanDetailValidationResult>> validateDetails(
            @Valid @RequestBody List<BizIPlanListParam> details,
            UserInfoToken userInfo) {
        
        try {
            log.info("开始校验表体数据，数据条数: {}", details != null ? details.size() : 0);
            
            BizIPlanDetailValidationResult result = bizIPlanDetailValidationService.validateDetails(details);
            
            log.info("表体数据校验完成，校验结果: {}, 错误数量: {}", 
                result.isValid() ? "通过" : "失败", result.getErrorCount());
            
            return ResponseEntity.ok(ResultObject.createInstance(true, "校验完成", result));
            
        } catch (Exception e) {
            log.error("表体数据校验异常", e);
            return ResponseEntity.ok(ResultObject.createInstance(false, "校验异常: " + e.getMessage()));
        }
    }

    /**
     * 校验表体数据列表（简化版本）
     *
     * @param details 表体数据列表
     * @param userInfo 用户信息
     * @return 错误信息字符串
     */
    @PostMapping("/validateDetailsSimple")
    @ApiOperation(value = "校验表体数据列表（简化版本）", notes = "对进口计划表体数据进行校验，返回简化的错误信息字符串")
    public ResponseEntity<ResultObject<String>> validateDetailsSimple(
            @Valid @RequestBody List<BizIPlanListParam> details,
            UserInfoToken userInfo) {
        
        try {
            log.info("开始简化校验表体数据，数据条数: {}", details != null ? details.size() : 0);
            
            String errorMessage = bizIPlanDetailValidationService.validateDetailsSimple(details);
            
            boolean isValid = errorMessage == null || errorMessage.trim().isEmpty();
            log.info("表体数据简化校验完成，校验结果: {}", isValid ? "通过" : "失败");
            
            return ResponseEntity.ok(ResultObject.createInstance(true, 
                isValid ? "校验通过" : "校验失败", errorMessage));
            
        } catch (Exception e) {
            log.error("表体数据简化校验异常", e);
            return ResponseEntity.ok(ResultObject.createInstance(false, "校验异常: " + e.getMessage()));
        }
    }

    /**
     * 校验商品名称重复性
     *
     * @param details 表体数据列表
     * @param userInfo 用户信息
     * @return 重复性校验错误信息列表
     */
    @PostMapping("/validateProductNameDuplication")
    @ApiOperation(value = "校验商品名称重复性", notes = "检查表体数据中是否存在重复的商品名称")
    public ResponseEntity<ResultObject<List<String>>> validateProductNameDuplication(
            @Valid @RequestBody List<BizIPlanListParam> details,
            UserInfoToken userInfo) {
        
        try {
            log.info("开始校验商品名称重复性，数据条数: {}", details != null ? details.size() : 0);
            
            List<String> duplicationErrors = bizIPlanDetailValidationService.validateProductNameDuplication(details);
            
            boolean hasErrors = duplicationErrors != null && !duplicationErrors.isEmpty();
            log.info("商品名称重复性校验完成，发现重复: {}, 重复项数量: {}", 
                hasErrors, hasErrors ? duplicationErrors.size() : 0);
            
            return ResponseEntity.ok(ResultObject.createInstance(true, 
                hasErrors ? "发现重复商品名称" : "无重复商品名称", duplicationErrors));
            
        } catch (Exception e) {
            log.error("商品名称重复性校验异常", e);
            return ResponseEntity.ok(ResultObject.createInstance(false, "校验异常: " + e.getMessage()));
        }
    }

    /**
     * 校验单个表体数据的必填字段
     *
     * @param detail 表体数据
     * @param userInfo 用户信息
     * @return 错误信息
     */
    @PostMapping("/validateSingleDetail")
    @ApiOperation(value = "校验单个表体数据", notes = "对单个进口计划表体数据进行必填字段校验")
    public ResponseEntity<ResultObject<String>> validateSingleDetail(
            @Valid @RequestBody BizIPlanListParam detail,
            UserInfoToken userInfo) {
        
        try {
            String productName = detail.getProductName() != null ? detail.getProductName() : "未知商品";
            log.info("开始校验单个表体数据，商品名称: {}", productName);
            
            String errorMessage = bizIPlanDetailValidationService.validateDetailRequiredFields(detail, productName);
            
            boolean isValid = errorMessage == null || errorMessage.trim().isEmpty();
            log.info("单个表体数据校验完成，商品名称: {}, 校验结果: {}", productName, isValid ? "通过" : "失败");
            
            return ResponseEntity.ok(ResultObject.createInstance(true, 
                isValid ? "校验通过" : "校验失败", errorMessage));
            
        } catch (Exception e) {
            log.error("单个表体数据校验异常", e);
            return ResponseEntity.ok(ResultObject.createInstance(false, "校验异常: " + e.getMessage()));
        }
    }
}
