package com.dcjet.cs.dto.importedCigarettes;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

/**
 * 进口计划表体数据校验结果
 *
 * <AUTHOR>
 * @date: 2025-3-13
 */
@Setter
@Getter
@ApiModel(value = "进口计划表体数据校验结果")
public class BizIPlanDetailValidationResult implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 校验是否通过
     */
    @ApiModelProperty("校验是否通过")
    private boolean valid;

    /**
     * 错误信息列表
     */
    @ApiModelProperty("错误信息列表")
    private List<String> errorMessages;

    /**
     * 错误信息汇总（用换行符连接）
     */
    @ApiModelProperty("错误信息汇总")
    private String errorSummary;

    public BizIPlanDetailValidationResult() {
        this.valid = true;
        this.errorMessages = new ArrayList<>();
        this.errorSummary = "";
    }

    /**
     * 添加错误信息
     *
     * @param errorMessage 错误信息
     */
    public void addErrorMessage(String errorMessage) {
        if (errorMessage != null && !errorMessage.trim().isEmpty()) {
            this.errorMessages.add(errorMessage);
            this.valid = false;
            updateErrorSummary();
        }
    }

    /**
     * 添加多个错误信息
     *
     * @param errorMessages 错误信息列表
     */
    public void addErrorMessages(List<String> errorMessages) {
        if (errorMessages != null && !errorMessages.isEmpty()) {
            this.errorMessages.addAll(errorMessages);
            this.valid = false;
            updateErrorSummary();
        }
    }

    /**
     * 更新错误信息汇总
     */
    private void updateErrorSummary() {
        this.errorSummary = String.join("\n", this.errorMessages);
    }

    /**
     * 检查是否有错误
     *
     * @return true表示有错误，false表示无错误
     */
    public boolean hasErrors() {
        return !this.valid;
    }

    /**
     * 获取错误数量
     *
     * @return 错误数量
     */
    public int getErrorCount() {
        return this.errorMessages.size();
    }
}
