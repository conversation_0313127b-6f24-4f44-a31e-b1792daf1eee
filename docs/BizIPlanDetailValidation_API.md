# 进口计划表体数据校验接口文档

## 概述

本文档描述了进口计划表体数据校验的独立接口，该接口从原有的 `BizIPlanService` 中提取了表体数据校验逻辑，提供了更灵活和可重用的校验服务。

## 接口列表

### 1. 校验表体数据列表（完整版本）

**接口地址：** `POST /v1/bizIPlanDetailValidation/validateDetails`

**功能描述：** 对进口计划表体数据进行全面校验，包括必填字段、格式、重复性等，返回详细的校验结果对象。

**请求参数：**
```json
[
  {
    "productName": "商品名称",
    "supplier": "供应商",
    "englishBrand": "英文品牌",
    "origin": "原产地",
    "planQuantity": 100.50,
    "unit": "箱",
    "curr": "USD",
    "unitPrice": 25.99,
    "totalAmount": 2609.495,
    "discountRate": 0.05,
    "discountAmount": 130.47
  }
]
```

**响应结果：**
```json
{
  "success": true,
  "message": "校验完成",
  "data": {
    "valid": false,
    "errorMessages": [
      "测试商品A的供应商不能为空",
      "商品名称 '重复商品' 在表体数据中重复出现2次"
    ],
    "errorSummary": "测试商品A的供应商不能为空\n商品名称 '重复商品' 在表体数据中重复出现2次",
    "errorCount": 2
  }
}
```

### 2. 校验表体数据列表（简化版本）

**接口地址：** `POST /v1/bizIPlanDetailValidation/validateDetailsSimple`

**功能描述：** 对进口计划表体数据进行校验，返回简化的错误信息字符串。

**请求参数：** 同上

**响应结果：**
```json
{
  "success": true,
  "message": "校验失败",
  "data": "测试商品A的供应商不能为空\n商品名称 '重复商品' 在表体数据中重复出现2次"
}
```

### 3. 校验商品名称重复性

**接口地址：** `POST /v1/bizIPlanDetailValidation/validateProductNameDuplication`

**功能描述：** 检查表体数据中是否存在重复的商品名称。

**请求参数：** 同上

**响应结果：**
```json
{
  "success": true,
  "message": "发现重复商品名称",
  "data": [
    "商品名称 '重复商品' 在表体数据中重复出现2次"
  ]
}
```

### 4. 校验单个表体数据

**接口地址：** `POST /v1/bizIPlanDetailValidation/validateSingleDetail`

**功能描述：** 对单个进口计划表体数据进行必填字段校验。

**请求参数：**
```json
{
  "productName": "商品名称",
  "supplier": "供应商",
  "englishBrand": "英文品牌",
  "origin": "原产地",
  "planQuantity": 100.50,
  "unit": "箱",
  "curr": "USD",
  "unitPrice": 25.99,
  "totalAmount": 2609.495,
  "discountRate": 0.05,
  "discountAmount": 130.47
}
```

**响应结果：**
```json
{
  "success": true,
  "message": "校验失败",
  "data": "商品名称的供应商不能为空\n商品名称的计划数量不能为空"
}
```

## 校验规则

### 必填字段校验
1. **供应商** - 不能为空，长度不能超过400个字节
2. **计划数量** - 不能为空，整数部分不能超过13位，小数部分不能超过6位
3. **计划数量单位** - 不能为空，长度不能超过40个字节
4. **币种** - 不能为空，长度不能超过20个字节
5. **计划单价** - 不能为空，整数部分不能超过14位，小数部分不能超过5位
6. **计划总金额** - 不能为空，整数部分不能超过14位，小数部分不能超过5位

### 可选字段校验
1. **英文品牌** - 长度不能超过400个字节
2. **原产地** - 长度不能超过200个字节
3. **折扣率** - 整数部分不能超过15位，小数部分不能超过4位
4. **折扣金额** - 整数部分不能超过14位，小数部分不能超过5位

### 重复性校验
- **商品名称重复性** - 检查同一批表体数据中是否存在重复的商品名称

## 错误信息格式

所有错误信息都使用商品名称来标识具体的表体条目，而不是使用索引号，这样更便于用户理解和定位问题。

例如：
- `"测试商品A的供应商不能为空"`
- `"商品名称 '重复商品' 在表体数据中重复出现2次"`

## 使用示例

### Java 代码示例

```java
@Resource
private BizIPlanDetailValidationService validationService;

// 校验表体数据列表
public void validatePlanDetails(List<BizIPlanListParam> details) {
    BizIPlanDetailValidationResult result = validationService.validateDetails(details);
    
    if (!result.isValid()) {
        // 处理校验失败的情况
        log.error("表体数据校验失败: {}", result.getErrorSummary());
        throw new ErrorException(400, result.getErrorSummary());
    }
    
    // 校验通过，继续业务逻辑
    log.info("表体数据校验通过");
}

// 简化校验
public void validatePlanDetailsSimple(List<BizIPlanListParam> details) {
    String errorMessage = validationService.validateDetailsSimple(details);
    
    if (StringUtils.isNotBlank(errorMessage)) {
        throw new ErrorException(400, errorMessage);
    }
}
```

### 前端调用示例

```javascript
// 校验表体数据
async function validateDetails(details) {
    try {
        const response = await fetch('/v1/bizIPlanDetailValidation/validateDetails', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(details)
        });
        
        const result = await response.json();
        
        if (result.success && result.data.valid) {
            console.log('校验通过');
            return true;
        } else {
            console.error('校验失败:', result.data.errorSummary);
            return false;
        }
    } catch (error) {
        console.error('校验异常:', error);
        return false;
    }
}
```

## 注意事项

1. **原有代码保持不变** - 这个独立的校验接口不会影响 `BizIPlanService` 中现有的校验逻辑
2. **错误信息本地化** - 错误信息目前为中文，如需支持多语言可以进一步扩展
3. **性能考虑** - 对于大量数据的校验，建议分批处理
4. **扩展性** - 可以根据业务需要添加更多的校验规则

## 测试

项目中包含了完整的单元测试，位于：
`ics-biz-services/src/test/java/com/dcjet/cs/importedCigarettes/service/BizIPlanDetailValidationServiceTest.java`

可以运行测试来验证校验服务的正确性。
