package com.dcjet.cs.dec.service;

import com.dcjet.cs.dec.dao.BizIWarehouseReceiptHeadMapper;
import com.dcjet.cs.dec.dao.BizIWarehouseReceiptListMapper;
import com.dcjet.cs.dec.mapper.BizIWarehouseReceiptHeadDtoMapper;
import com.dcjet.cs.dec.mapper.BizIWarehouseReceiptListDtoMapper;
import com.dcjet.cs.dec.model.BizIWarehouseReceiptHead;
import com.dcjet.cs.dec.model.BizIWarehouseReceiptList;
import com.dcjet.cs.dto.dec.*;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.xdo.common.base.service.BaseService;
import com.xdo.common.exception.ErrorException;
import com.xdo.common.token.UserInfoToken;
import com.xdo.domain.PageParam;
import com.xdo.domain.ResultObject;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import tk.mybatis.mapper.common.Mapper;
import xdoi18n.XdoI18nUtil;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * BizIOrderHead业务逻辑处理类
 *
 * <AUTHOR>
 * @date 2025-03-07 15:37:18
 * 翻译使用：throw new ErrorException(400, XdoI18nUtil.t("xxxxxxxxxx"));
 */
@Service
public class BizIWarehouseReceiptListService extends BaseService<BizIWarehouseReceiptList> {

    private static final Logger log = LoggerFactory.getLogger(BizIWarehouseReceiptListService.class);

    @Resource
    private BizIWarehouseReceiptListMapper mapper;

    @Resource
    private BizIWarehouseReceiptHeadMapper headMapper;

    @Resource
    private BizIWarehouseReceiptListDtoMapper dtoMapper;

    @Override
    public Mapper<BizIWarehouseReceiptList> getMapper() {
        return mapper;
    }



    /**
     * 获取分页信息
     *
     * @param param 查询参数
     * @param pageParam               分页参数
     * @return 分页结果
     */
    public ResultObject<List<BizIWarehouseReceiptListDto>> getListPaged(BizIWarehouseReceiptListParam param, PageParam pageParam, UserInfoToken userInfo) {
        // 启用分页查询
        BizIWarehouseReceiptList list = dtoMapper.toPo(param);
        list.setTradeCode(userInfo.getCompany());
        //先计算
        List<BizIWarehouseReceiptList> listDatas = mapper.getList(list);
        for (BizIWarehouseReceiptList data:listDatas) {
            //税金金额小计（D）
            BigDecimal tariff= data.getTariff()!=null?data.getTariff():BigDecimal.ZERO;
            BigDecimal consumptionTax= data.getConsumptionTax()!=null?data.getConsumptionTax():BigDecimal.ZERO;
            BigDecimal valueAddedTax= data.getValueAddedTax()!=null?data.getValueAddedTax():BigDecimal.ZERO;
            BigDecimal rmbPrices= data.getRmbPrices()!=null?data.getRmbPrices():BigDecimal.ZERO;
            //税金金额小计
            data.setTaxAmount(tariff.add(consumptionTax).add(valueAddedTax));
            //成本金额小计
            data.setCostAmount(rmbPrices.add(tariff).add(consumptionTax));
            //合计金额
            data.setTotalAmount(rmbPrices.add(data.getTaxAmount()));
            mapper.updateByPrimaryKey(data);
        }
        Page<BizIWarehouseReceiptList> page = PageHelper.startPage(pageParam.getPage(), pageParam.getLimit(), pageParam.getSortOrderContent())
            .doSelectPage(() -> mapper.getList(list));
        // 将PO转为DTO返回给前端
        List<BizIWarehouseReceiptListDto> dtos = page.getResult().stream()
            .map(dtoMapper::toDto)
            .collect(Collectors.toList());

        return ResultObject.createInstance(dtos, (int) page.getTotal(), page.getPageNum());
    }

    /**
     * 修改记录
     *
     * @param param 更新参数
     * @param userInfo                用户信息
     * @return 更新后的DTO对象
     */
    @Transactional(rollbackFor = Exception.class)
    public BizIWarehouseReceiptListDto update(BizIWarehouseReceiptListParam param, UserInfoToken userInfo) {
        BizIWarehouseReceiptList list = mapper.selectByPrimaryKey(param.getSid());
        BizIWarehouseReceiptHead head = headMapper.selectByPrimaryKey(list.getParentId());
        dtoMapper.updatePo(param, list);
        list.setUpdateUserName(userInfo.getUserName());
        list.setUpdateUser(userInfo.getUserNo());
        list.setUpdateTime(new Date());
        //修改保存时，根据外币货价重新计算外币单价
        if (null!=list.getForeignPrices() && null!=list.getQty() && BigDecimal.ZERO.compareTo(list.getQty()) != 0){
            try {
//                // 计算外币单价：外币货价 ÷ 数量 ÷ (折扣率 ÷ 100)
//                if (head.getDiscountRate() != null && BigDecimal.ZERO.compareTo(head.getDiscountRate()) != 0) {
//                    // 计算折扣率因子 (折扣率 ÷ 100)
//                    BigDecimal discountFactor = head.getDiscountRate().divide(BigDecimal.valueOf(100), 8, RoundingMode.HALF_UP);
//                    // 确保折扣因子不为零
//                    if (BigDecimal.ZERO.compareTo(discountFactor) != 0) {
//                        list.setForeignUnitPrice(list.getForeignPrices().divide(list.getQty(), 2, RoundingMode.HALF_UP).divide(BigDecimal.ONE.subtract(discountFactor), 2, RoundingMode.HALF_UP));
//                    } else {
                        // 如果折扣因子为零，直接使用外币货价除以数量
                        list.setForeignUnitPrice(list.getForeignPrices().divide(list.getQty(), 2, RoundingMode.HALF_UP));
//                    }
//                } else {
                    // 如果没有折扣率，直接使用外币货价除以数量(修改为不考虑折扣率 2025-05-28)
                    list.setForeignUnitPrice(list.getForeignPrices().divide(list.getQty(), 2, RoundingMode.HALF_UP));
//                }

                if (null!=head.getRate()){
                    //人民币货价
                    list.setRmbPrices(head.getRate().multiply(list.getForeignPrices()).setScale(8, RoundingMode.HALF_UP));
                    //人民币单价
                    // 计算外币货价：单价 * 数量 * (1 - 折扣率/100)
                    list.setRmbUnitPrice(list.getRmbPrices().divide(list.getQty(), 8, RoundingMode.HALF_UP));
//                    BigDecimal amount = list.getRmbUnitPrice().multiply(list.getQty());
//                    list.setRmbPrices(amount);
                    //商品金额小计
                    list.setProducAmount(list.getRmbPrices());
                }
            } catch (Exception e) {
                log.error("计算外币单价时发生错误: " + e.getMessage(), e);
                // 发生异常时，使用安全的计算方式
                list.setForeignUnitPrice(list.getForeignPrices().divide(list.getQty(), 2, RoundingMode.HALF_UP));

                if (null!=head.getRate()){
                    //人民币单价
                    list.setRmbUnitPrice(head.getRate().multiply(list.getForeignUnitPrice()).setScale(8, RoundingMode.HALF_UP));
                    //人民币货价
                    list.setRmbPrices(head.getRate().multiply(list.getForeignPrices()).setScale(2, RoundingMode.HALF_UP));
                    //商品金额小计
                    list.setProducAmount(list.getRmbPrices());
                }
            }
        }


        // 更新数据
        int update = mapper.updateByPrimaryKey(list);
        return update > 0 ? dtoMapper.toDto(list) : null;
    }


    public ResultObject getSumData(BizIWarehouseReceiptListParam param, UserInfoToken userInfo) {
        ResultObject resultObject = ResultObject.createInstance(true, "获取汇总数据成功！");
        // Assert.hasLength(param.getHeadId(), "headId不能为空!");
        if (StringUtils.isBlank(param.getParentId())) {
            throw new ErrorException(400, XdoI18nUtil.t("headId不能为空!"));
        }
        // 获取汇总数据
        BizIWarehouseReceiptList sumData = mapper.getSumData(param.getParentId());
        resultObject.setData(sumData);
        return resultObject;
    }

    /**
     * 提取税金
     * @param parentId
     * @param userInfo
     * @return
     */
    public ResultObject extractTaxes(String parentId, UserInfoToken userInfo) {
        ResultObject resultObject = ResultObject.createInstance(true, "提取税金成功！");
        BizIWarehouseReceiptHead bizIWarehouseReceiptHead = headMapper.selectByPrimaryKey(parentId);

        //进货单号
        String warehouseReceiptNumber = bizIWarehouseReceiptHead.getWarehouseReceiptNumber();

        mapper.updateListTaxes(warehouseReceiptNumber, parentId, userInfo.getUserNo(), userInfo.getCompany());

        return resultObject;
    }

    public ResultObject<BizIWarehouseReceiptListDto> getWarehouseReceiptListBySid(String sid, UserInfoToken userInfo) {
        ResultObject<BizIWarehouseReceiptListDto> resultObject = ResultObject.createInstance(true, "获取成功！");
        BizIWarehouseReceiptList bizIWarehouseReceiptList = mapper.selectByPrimaryKey(sid);
        BizIWarehouseReceiptListDto dto = dtoMapper.toDto(bizIWarehouseReceiptList);
        resultObject.setData(dto);
        return resultObject;
    }
}