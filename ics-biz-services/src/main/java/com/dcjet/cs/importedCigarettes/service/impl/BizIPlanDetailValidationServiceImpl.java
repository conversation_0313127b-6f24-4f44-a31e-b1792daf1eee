package com.dcjet.cs.importedCigarettes.service.impl;

import com.dcjet.cs.dto.importedCigarettes.BizIPlanDetailValidationResult;
import com.dcjet.cs.dto.importedCigarettes.BizIPlanListParam;
import com.dcjet.cs.importedCigarettes.service.BizIPlanDetailValidationService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.*;

/**
 * 进口计划表体数据校验服务实现类
 *
 * <AUTHOR>
 * @date: 2025-3-13
 */
@Slf4j
@Service
public class BizIPlanDetailValidationServiceImpl implements BizIPlanDetailValidationService {

    @Override
    public BizIPlanDetailValidationResult validateDetails(List<BizIPlanListParam> details) {
        BizIPlanDetailValidationResult result = new BizIPlanDetailValidationResult();

        if (CollectionUtils.isEmpty(details)) {
            return result;
        }

        // 校验每个表体数据的必填字段
        for (BizIPlanListParam detail : details) {
            String productName = StringUtils.isNotBlank(detail.getProductName()) ?
                detail.getProductName() : "未知商品";
            String validationErrors = validateDetailRequiredFields(detail, productName);
            if (StringUtils.isNotBlank(validationErrors)) {
                result.addErrorMessage(validationErrors);
            }
        }

        // 校验商品名称重复性
        List<String> duplicationErrors = validateProductNameDuplication(details);
        result.addErrorMessages(duplicationErrors);

        return result;
    }

    @Override
    public String validateDetailRequiredFields(BizIPlanListParam detail, String productName) {
        StringJoiner errors = new StringJoiner("\n");

        // 使用传入的商品名称，如果为空则使用detail中的商品名称
        String displayName = StringUtils.isNotBlank(productName) ? productName :
            (StringUtils.isNotBlank(detail.getProductName()) ? detail.getProductName() : "未知商品");

        // 2. 供应商校验
        if (StringUtils.isBlank(detail.getSupplier())) {
            errors.add(displayName + "的供应商不能为空");
        } else if (detail.getSupplier().length() > 400) {
            errors.add(displayName + "的供应商长度不能超过400个字节");
        }

        // 3. 英文品牌校验
        if (detail.getEnglishBrand() != null && detail.getEnglishBrand().length() > 400) {
            errors.add(displayName + "的英文品牌长度不能超过400个字节");
        }

        // 4. 原产地校验
        if (detail.getOrigin() != null && detail.getOrigin().length() > 200) {
            errors.add(displayName + "的原产地长度不能超过200个字节");
        }

        // 5. 计划数量校验
        if (detail.getPlanQuantity() == null) {
            errors.add(displayName + "的计划数量不能为空");
        } else {
            // 检查数字格式
            String planQuantityStr = detail.getPlanQuantity().toString();
            String[] parts = planQuantityStr.split("\\.");
            if (parts.length > 1) {
                if (parts[0].length() > 13) {
                    errors.add(displayName + "的计划数量整数部分不能超过13位");
                }
                if (parts[1].length() > 6) {
                    errors.add(displayName + "的计划数量小数部分不能超过6位");
                }
            } else if (parts[0].length() > 13) {
                errors.add(displayName + "的计划数量整数部分不能超过13位");
            }
        }

        // 6. 计划数量单位校验
        if (StringUtils.isBlank(detail.getUnit())) {
            errors.add(displayName + "的计划数量单位不能为空");
        } else if (detail.getUnit().length() > 40) {
            errors.add(displayName + "的计划数量单位长度不能超过40个字节");
        }

        // 7. 币种校验
        if (StringUtils.isBlank(detail.getCurr())) {
            errors.add(displayName + "的币种不能为空");
        } else if (detail.getCurr().length() > 20) {
            errors.add(displayName + "的币种长度不能超过20个字节");
        }

        // 8. 计划单价校验
        if (detail.getUnitPrice() == null) {
            errors.add(displayName + "的计划单价不能为空");
        } else {
            // 检查数字格式
            String unitPriceStr = detail.getUnitPrice().toString();
            String[] parts = unitPriceStr.split("\\.");
            if (parts.length > 1) {
                if (parts[0].length() > 14) {
                    errors.add(displayName + "的计划单价整数部分不能超过14位");
                }
                if (parts[1].length() > 5) {
                    errors.add(displayName + "的计划单价小数部分不能超过5位");
                }
            } else if (parts[0].length() > 14) {
                errors.add(displayName + "的计划单价整数部分不能超过14位");
            }
        }

        // 9. 计划总金额校验
        if (detail.getTotalAmount() == null) {
            errors.add(displayName + "的计划总金额不能为空");
        } else {
            // 检查数字格式
            String totalAmountStr = detail.getTotalAmount().toString();
            String[] parts = totalAmountStr.split("\\.");
            if (parts.length > 1) {
                if (parts[0].length() > 14) {
                    errors.add(displayName + "的计划总金额整数部分不能超过14位");
                }
                if (parts[1].length() > 5) {
                    errors.add(displayName + "的计划总金额小数部分不能超过5位");
                }
            } else if (parts[0].length() > 14) {
                errors.add(displayName + "的计划总金额整数部分不能超过14位");
            }
        }

        // 10. 折扣率校验
        if (detail.getDiscountRate() != null) {
            String discountRateStr = detail.getDiscountRate().toString();
            String[] parts = discountRateStr.split("\\.");
            if (parts.length > 1) {
                if (parts[0].length() > 15) {
                    errors.add(displayName + "的折扣率整数部分不能超过15位");
                }
                if (parts[1].length() > 4) {
                    errors.add(displayName + "的折扣率小数部分不能超过4位");
                }
            } else if (parts[0].length() > 15) {
                errors.add(displayName + "的折扣率整数部分不能超过15位");
            }
        }

        // 11. 折扣金额校验
        if (detail.getDiscountAmount() != null) {
            String discountAmountStr = detail.getDiscountAmount().toString();
            String[] parts = discountAmountStr.split("\\.");
            if (parts.length > 1) {
                if (parts[0].length() > 14) {
                    errors.add(displayName + "的折扣金额整数部分不能超过14位");
                }
                if (parts[1].length() > 5) {
                    errors.add(displayName + "的折扣金额小数部分不能超过5位");
                }
            } else if (parts[0].length() > 14) {
                errors.add(displayName + "的折扣金额整数部分不能超过14位");
            }
        }

        return errors.length() > 0 ? errors.toString() : "";
    }

    @Override
    public List<String> validateProductNameDuplication(List<BizIPlanListParam> details) {
        List<String> errors = new ArrayList<>();

        if (CollectionUtils.isEmpty(details)) {
            return errors;
        }

        Map<String, Integer> productNameCount = new HashMap<>();

        // 统计商品名称出现次数
        for (BizIPlanListParam detail : details) {
            if (StringUtils.isNotBlank(detail.getProductName())) {
                productNameCount.put(detail.getProductName(),
                        productNameCount.getOrDefault(detail.getProductName(), 0) + 1);
            }
        }

        // 检查商品名称重复
        for (Map.Entry<String, Integer> entry : productNameCount.entrySet()) {
            if (entry.getValue() > 1) {
                errors.add("商品名称 '" + entry.getKey() + "' 在表体数据中重复出现" + entry.getValue() + "次");
            }
        }

        return errors;
    }

    @Override
    public String validateDetailsSimple(List<BizIPlanListParam> details) {
        BizIPlanDetailValidationResult result = validateDetails(details);
        return result.getErrorSummary();
    }
}
