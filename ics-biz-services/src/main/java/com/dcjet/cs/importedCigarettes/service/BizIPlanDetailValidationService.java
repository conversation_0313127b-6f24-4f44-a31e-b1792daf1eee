package com.dcjet.cs.importedCigarettes.service;

import com.dcjet.cs.dto.importedCigarettes.BizIPlanDetailValidationResult;
import com.dcjet.cs.dto.importedCigarettes.BizIPlanListParam;

import java.util.List;

/**
 * 进口计划表体数据校验服务接口
 *
 * <AUTHOR>
 * @date: 2025-3-13
 */
public interface BizIPlanDetailValidationService {

    /**
     * 校验表体数据列表
     *
     * @param details 表体数据列表
     * @return 校验结果
     */
    BizIPlanDetailValidationResult validateDetails(List<BizIPlanListParam> details);

    /**
     * 校验单个表体数据的必填字段
     *
     * @param detail 表体数据
     * @param productName 商品名称（用于错误信息标识）
     * @return 错误信息，如果没有错误则返回空字符串
     */
    String validateDetailRequiredFields(BizIPlanListParam detail, String productName);

    /**
     * 校验商品名称重复性
     *
     * @param details 表体数据列表
     * @return 重复性校验错误信息列表
     */
    List<String> validateProductNameDuplication(List<BizIPlanListParam> details);

    /**
     * 校验表体数据列表（简化版本，只返回错误信息字符串）
     *
     * @param details 表体数据列表
     * @return 所有错误信息，用换行符连接，如果没有错误则返回空字符串
     */
    String validateDetailsSimple(List<BizIPlanListParam> details);
}
