package com.dcjet.cs.importedCigarettes.service;

import com.dcjet.cs.dto.importedCigarettes.BizIPlanDetailValidationResult;
import com.dcjet.cs.dto.importedCigarettes.BizIPlanListParam;
import com.dcjet.cs.importedCigarettes.service.impl.BizIPlanDetailValidationServiceImpl;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.junit.jupiter.MockitoExtension;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;

/**
 * 进口计划表体数据校验服务测试类
 *
 * <AUTHOR>
 * @date: 2025-3-13
 */
@ExtendWith(MockitoExtension.class)
class BizIPlanDetailValidationServiceTest {

    @InjectMocks
    private BizIPlanDetailValidationServiceImpl validationService;

    private BizIPlanListParam validDetail;
    private BizIPlanListParam invalidDetail;

    @BeforeEach
    void setUp() {
        // 创建有效的表体数据
        validDetail = new BizIPlanListParam();
        validDetail.setProductName("测试商品A");
        validDetail.setSupplier("测试供应商");
        validDetail.setEnglishBrand("Test Brand");
        validDetail.setOrigin("中国");
        validDetail.setPlanQuantity(new BigDecimal("100.50"));
        validDetail.setUnit("箱");
        validDetail.setCurr("USD");
        validDetail.setUnitPrice(new BigDecimal("25.99"));
        validDetail.setTotalAmount(new BigDecimal("2609.495"));
        validDetail.setDiscountRate(new BigDecimal("0.05"));
        validDetail.setDiscountAmount(new BigDecimal("130.47"));

        // 创建无效的表体数据
        invalidDetail = new BizIPlanListParam();
        invalidDetail.setProductName("测试商品B");
        // 故意留空必填字段来测试校验
        invalidDetail.setSupplier(null);
        invalidDetail.setUnit(null);
        invalidDetail.setCurr(null);
        invalidDetail.setPlanQuantity(null);
        invalidDetail.setUnitPrice(null);
        invalidDetail.setTotalAmount(null);
    }

    @Test
    void testValidateDetails_WithValidData_ShouldPass() {
        // 准备测试数据
        List<BizIPlanListParam> details = new ArrayList<>();
        details.add(validDetail);

        // 执行校验
        BizIPlanDetailValidationResult result = validationService.validateDetails(details);

        // 验证结果
        assertTrue(result.isValid(), "有效数据应该通过校验");
        assertEquals(0, result.getErrorCount(), "有效数据不应该有错误");
        assertTrue(result.getErrorSummary().isEmpty(), "有效数据错误摘要应该为空");
    }

    @Test
    void testValidateDetails_WithInvalidData_ShouldFail() {
        // 准备测试数据
        List<BizIPlanListParam> details = new ArrayList<>();
        details.add(invalidDetail);

        // 执行校验
        BizIPlanDetailValidationResult result = validationService.validateDetails(details);

        // 验证结果
        assertFalse(result.isValid(), "无效数据应该校验失败");
        assertTrue(result.getErrorCount() > 0, "无效数据应该有错误");
        assertFalse(result.getErrorSummary().isEmpty(), "无效数据错误摘要不应该为空");
        
        // 验证错误信息包含商品名称
        assertTrue(result.getErrorSummary().contains("测试商品B"), "错误信息应该包含商品名称");
    }

    @Test
    void testValidateProductNameDuplication_WithDuplicates_ShouldDetect() {
        // 准备测试数据 - 两个相同商品名称的数据
        List<BizIPlanListParam> details = new ArrayList<>();
        
        BizIPlanListParam detail1 = new BizIPlanListParam();
        detail1.setProductName("重复商品");
        details.add(detail1);
        
        BizIPlanListParam detail2 = new BizIPlanListParam();
        detail2.setProductName("重复商品");
        details.add(detail2);

        // 执行校验
        List<String> errors = validationService.validateProductNameDuplication(details);

        // 验证结果
        assertFalse(errors.isEmpty(), "应该检测到重复的商品名称");
        assertTrue(errors.get(0).contains("重复商品"), "错误信息应该包含重复的商品名称");
        assertTrue(errors.get(0).contains("重复出现2次"), "错误信息应该说明重复次数");
    }

    @Test
    void testValidateProductNameDuplication_WithoutDuplicates_ShouldPass() {
        // 准备测试数据 - 不同商品名称的数据
        List<BizIPlanListParam> details = new ArrayList<>();
        
        BizIPlanListParam detail1 = new BizIPlanListParam();
        detail1.setProductName("商品A");
        details.add(detail1);
        
        BizIPlanListParam detail2 = new BizIPlanListParam();
        detail2.setProductName("商品B");
        details.add(detail2);

        // 执行校验
        List<String> errors = validationService.validateProductNameDuplication(details);

        // 验证结果
        assertTrue(errors.isEmpty(), "不同商品名称不应该有重复错误");
    }

    @Test
    void testValidateDetailRequiredFields_WithValidData_ShouldPass() {
        // 执行校验
        String errorMessage = validationService.validateDetailRequiredFields(validDetail, "测试商品A");

        // 验证结果
        assertTrue(errorMessage.isEmpty(), "有效数据不应该有错误信息");
    }

    @Test
    void testValidateDetailRequiredFields_WithInvalidData_ShouldFail() {
        // 执行校验
        String errorMessage = validationService.validateDetailRequiredFields(invalidDetail, "测试商品B");

        // 验证结果
        assertFalse(errorMessage.isEmpty(), "无效数据应该有错误信息");
        assertTrue(errorMessage.contains("测试商品B"), "错误信息应该包含商品名称");
        assertTrue(errorMessage.contains("供应商不能为空"), "应该检测到供应商为空");
        assertTrue(errorMessage.contains("计划数量不能为空"), "应该检测到计划数量为空");
    }

    @Test
    void testValidateDetailsSimple_ShouldReturnErrorString() {
        // 准备测试数据
        List<BizIPlanListParam> details = new ArrayList<>();
        details.add(invalidDetail);

        // 执行校验
        String errorMessage = validationService.validateDetailsSimple(details);

        // 验证结果
        assertFalse(errorMessage.isEmpty(), "无效数据应该返回错误信息");
        assertTrue(errorMessage.contains("测试商品B"), "错误信息应该包含商品名称");
    }
}
